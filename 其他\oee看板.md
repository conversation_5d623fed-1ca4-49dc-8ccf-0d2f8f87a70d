# 看板需求描述

## 数据存储

1、每个设备对应一个数据库集合，集合名称为设备编码加上设备日志类型

设备编码: HJ-PR-886,HJ-PR-223。
日志类型:

```enum
production //生产日志
event //事件日志
error //错误日志
```

数据库集合:
HJ-PR-886_production、HJ-PR-886_event、HJ-PR-223_error

2、设备信息维护表

用于记录设备对应的设备类型

```table
device_info:{
code: string //设备编码
type: enum   //设备类型
}


type: [
print_gkg   // gkg打印机
smt_samsung // 三星贴片机
smt_npm // 松下贴片机
smt_yamaha // 雅马哈贴片机
aoi_yamaha // 雅马哈aoi
]
```

根据对应的设备类型进行对应的查询。

## 查询接口

1、线体列表集合，获取车间所有的线体。

2、线体设备列表集合，依据线体编码获取线体下的所有设备，包括设备编码，设备CT时间等信息。

3、单个设备的各项查询指标。包括设备日志，故障日志等。

## 前端页面

1、看板页面，下拉选择框，选择对应线体。切换到对应线体大屏。大屏包括该线体下所有设备。

2、线体大屏，每个设备显示其ct时间

3、点击每个设备进入设备对应的页面。
