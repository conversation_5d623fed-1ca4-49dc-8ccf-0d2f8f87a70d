[
  {
    $match: {
      time: {
        $gte: ISODate("2025-04-01T00:00:00Z"),
        $lte: ISODate("2025-05-01T00:00:00Z")
      },
      normalflag: { $lte: true }
    }
  },
  { $sort: { time: 1 } },
  {
    $setWindowFields: {
      partitionBy: null,
      sortBy: { time: 1 },
      output: {
        prevProduct: {
          $shift: {
            output: "$productmodel",
            by: -1,
            default: null
          }
        }
      }
    }
  },
  {
    $setWindowFields: {
      partitionBy: null,
      sortBy: { time: 1 },
      output: {
        groupId: {
          $sum: {
            $cond: [
              {
                $ne: [
                  "$productmodel",
                  "$prevProduct"
                ]
              },
              1,
              0
            ]
          },
          window: {
            documents: ["unbounded", "current"]
          }
        }
      }
    }
  },
  {
    $addFields: {
      runtime: {
        $ifNull: ["$samsungindex.runtime", 0]
      }, // 运行时长为 samsungindex.runtime 或 0
      stoptime: {
        $add: [
          {
            $ifNull: ["$samsungindex.stoptime", 0]
          },
          {
            $ifNull: ["$samsungindex.idletime", 0]
          } // stoptime = 停机时间 + 空闲时间
        ]
      },
      totaltime: {
        $add: [
          {
            $ifNull: ["$samsungindex.runtime", 0]
          },
          {
            $ifNull: ["$samsungindex.stoptime", 0]
          },
          {
            $ifNull: ["$samsungindex.idletime", 0]
          } // totaltime = runtime + stoptime + idletime
        ]
      },
      moduleNum: {
        $ifNull: ["$samsungindex.pcbcount", 0]
      }, // 模块数量
      boardNum: {
        $ifNull: ["$samsungindex.panelcount", 0]
      } // 板数量
    }
  },
  {
    $group: {
      _id: {
        productmodel: "$productmodel",
        groupId: "$groupId"
      },
      runtime: { $sum: "$runtime" }, // 汇总运行时长
      stoptime: { $sum: "$stoptime" }, // 汇总停机时长
      totaltime: { $sum: "$totaltime" }, // 汇总总时长
      totalModuleNum: { $sum: "$moduleNum" }, // 总模块数
      totalBoardNum: { $sum: "$boardNum" } // 总板数
    }
  },
  {
    $project: {
      runtime: 1,
      stoptime: 1,
      totaltime: 1,
      moduleNum: "$totalModuleNum", // 直接传递汇总后的模块数
      boardNum: "$totalBoardNum", // 直接传递汇总后的板数
      flatNum: {
        $cond: {
          if: { $gt: ["$totalBoardNum", 0] }, // 当板数 > 0 时
          then: {
            $ceil: {
              $divide: [
                "$totalModuleNum",
                "$totalBoardNum"
              ] // 计算模块数/板数，结果向上取整
            }
          },
          else: 0 // 否则 flatNum = 0
        }
      }
    }
  }
]