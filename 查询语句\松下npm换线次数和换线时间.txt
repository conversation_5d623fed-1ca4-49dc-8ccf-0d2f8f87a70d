[
  {
    $match: {
      time: {
        $gte: ISODate("2025-04-19T00:00:00Z"),
        $lte: ISODate("2025-04-20T00:00:00Z")
      }
    }
  },
  {
    $sort: {
      time: 1
    }
  },
  {
    $setWindowFields: {
      partitionBy: null,
      sortBy: {
        time: 1,
        totaltime: -1
      },
      output: {
        prevproductmodel: {
          $shift: {
            output: "$productmodel",
            by: -1
          }
        },
        prevcount: {
          $shift: {
            output: "$raw.count.board",
            by: -1
          }
        }
      }
    }
  },
  {
    $match: {
      prevcount: {
        $ne: null
      }
    }
  },
  {
    $addFields: {
      C: {
        $cond: {
          if: {
            $gt: [
              "$raw.count.board",
              "$prevcount"
            ]
          },
          then: {
            $subtract: [
              "$raw.count.board",
              "$prevcount"
            ]
          },
          else: 0
        }
      },
      isDifferentProduct: {
        $ne: [
          "$productmodel",
          "$prevproductmodel"
        ]
      }
    }
  },
  {
    $setWindowFields: {
      partitionBy: null,
      sortBy: {
        time: 1,
        totaltime: -1
      },
      output: {
        groupId: {
          $sum: {
            $cond: [
              {
                $ne: ["$C", 0]
              },
              1,
              0
            ]
          },
          window: {
            documents: ["unbounded", "current"]
          }
        }
      }
    }
  },
  {
    $match: {
      C: 0
    }
  },
  {
    $group: {
      _id: "$groupId",
      firstDoc: {
        $first: "$$ROOT"
      },
      lastDoc: {
        $last: "$$ROOT"
      },
      allC: {
        $push: "$C"
      },
      hasLineChange: {
        $push: "$isDifferentProduct"
      },
      count: {
        $sum: 1
      }
    }
  },
  {
    $match: {
      $expr: {
        $and: [
          {
            $eq: [
              {
                $max: "$allC"
              },
              0
            ]
          },
          {
            $anyElementTrue: "$hasLineChange"
          }
        ]
      }
    }
  },
  {
    $addFields: {
      difference: {
        $subtract: [
          "$lastDoc.raw.count.board",
          "$firstDoc.raw.count.board"
        ]
      },
      timeDifference: {
        $subtract: [
          "$lastDoc.time",
          "$firstDoc.time"
        ]
      },
      startTime: "$firstDoc.time",
      endTime: "$lastDoc.time"
    }
  },
  {
    $group: {
      _id: null,
      totalDifference: {
        $sum: "$difference"
      },
      totalTimeDifference: {
        $sum: "$timeDifference"
      },
      blockCount: {
        $sum: 1
      },
      blocks: {
        $push: {
          groupId: "$_id",
          startTime: "$startTime",
          endTime: "$endTime",
          timeDifference: "$timeDifference",
          difference: "$difference"
        }
      }
    }
  },
  {
    $project: {
      _id: 0,
      totalDifference: 1,
      totalTimeDifference: {
        $divide: ["$totalTimeDifference", 1000]
      },
      blockCount: 1,
      blocks: 1
    }
  }
]