from pymongo import MongoClient

def drop_all_hj_collections_indexes():
    # 连接MongoDB
    client = MongoClient('***********************************************/')
    
    # 获取数据库实例 - 请将'your_database'替换为实际的数据库名
    db = client['oee']
    
    # 获取所有集合名称
    collections = db.list_collection_names()
    
    # 筛选出以'HJ'开头的集合
    hj_collections = [coll for coll in collections if coll.startswith('HJ')]
    
    # 遍历每个集合并删除索引
    for collection_name in hj_collections:
        collection = db[collection_name]
        
        # 获取所有索引信息
        indexes = list(collection.list_indexes())
        
        # 删除除_id之外的所有索引
        for index in indexes:
            if index['name'] != '_id_':  # 跳过_id索引
                try:
                    collection.drop_index(index['name'])
                    print(f"成功删除集合 {collection_name} 中的索引: {index['name']}")
                except Exception as e:
                    print(f"删除集合 {collection_name} 中的索引 {index['name']} 时出错: {str(e)}")
    
    print("索引删除操作完成")
    client.close()

if __name__ == "__main__":
    try:
        drop_all_hj_collections_indexes()
    except Exception as e:
        print(f"执行过程中出现错误: {str(e)}")