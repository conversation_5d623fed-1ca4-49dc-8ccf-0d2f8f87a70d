[
  {
    $match: {
      time: {
        $gte: ISODate("2025-04-19T00:00:00Z"),
        $lte: ISODate("2025-04-20T00:00:00Z")
      }
    }
  },
  {
    $sort: { time: 1 }
  },
  {
    $setWindowFields: {
      sortBy: { time: 1 },
      output: {
        prevModel: {
          $shift: {
            output: "$productmodel",
            by: -1,
            default: null
          }
        },
        prevTime: {
          $shift: {
            output: "$time",
            by: -1,
            default: null
          }
        }
      }
    }
  },
  {
    $addFields: {
      overtime: {
        $cond: [
          {
            $ne: ["$productmodel", "$prevModel"]
          },
          {
            $subtract: [
              "$productionstarttime",
              "$prevTime"
            ]
          },
          0
        ]
      },
      // 新增换线标记字段
      isChangeover: {
        $cond: [
          {
            $and: [
              {
                $ne: [
                  "$productmodel",
                  "$prevModel"
                ]
              },
              { $ne: ["$prevModel", null] } // 排除第一条记录
            ]
          },
          1,
          0
        ]
      }
    }
  },
  {
    $skip: 1
  },
  {
    $group: {
      _id: null,
      totalOvertime: { $sum: "$overtime" },
      // 新增换线次数统计
      changeoverCount: { $sum: "$isChangeover" }
    }
  }
]