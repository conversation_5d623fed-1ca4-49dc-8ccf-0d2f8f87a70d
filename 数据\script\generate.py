import csv
import random
from datetime import datetime, timedelta

# 设定输出文件名
output_file = "test_data_with_enums.csv"

# 定义OeeException分类枚举值
oee_exceptions = [
    "DEVICE_EXCEPTION", "PROCESS_EXCEPTION", "QUALITY_EXCEPTION", 
    "PRODUCTION_EXCEPTION", "HUMAN_EXCEPTION", "MAINTENANCE", "PLAN"
]

# 定义随机原因池（可自定义更详细原因）
causes = [
    "停机换料", "设备故障", "工艺调整", "不合格率高", 
    "生产计划变更", "人为失误", "保养周期", "计划外任务"
]

# 时间范围
start_date = datetime(2025, 1, 1, 0, 0, 0)
end_date = datetime(2025, 12, 31, 23, 59, 59)

# 时间生成函数
def random_datetime(start, end):
    return start + timedelta(seconds=random.randint(0, int((end - start).total_seconds())))

# 生成并保存CSV
with open(output_file, mode="w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    
    # 写入表头
    headers = [
        "classification", "cause", "startTime", "endTime", 
        "responsible", "department", "writer", "writeTime",
        "confirm", "confirmTime", "_class"
    ]
    writer.writerow(headers)
    
    # 写入50条数据
    for i in range(50):
        # 随机日期时间生成
        start_time = random_datetime(start_date, end_date)
        end_time = random_datetime(start_time, start_time + timedelta(hours=12))
        write_time = random_datetime(end_time, end_time + timedelta(hours=2))
        confirm_time = random_datetime(write_time, write_time + timedelta(hours=1))
        
        # 构造每行数据
        row = [
            random.choice(oee_exceptions),  # 随机分类
            random.choice(causes),  # 随机原因
            start_time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
            end_time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
            f"测试{random.randint(1, 5)}",  # 随机负责人
            f"测试部门{random.randint(1, 3)}",  # 随机部门
            f"测试{random.randint(1, 5)}",  # 随机记录人
            write_time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
            f"确认人{random.randint(1, 3)}",  # 随机确认人
            confirm_time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
            "com.github.cret.web.oee.document.analyze.Abnormal"  # 固定class
        ]
        
        # 写入数据
        writer.writerow(row)

print(f"成功生成了 50 条测试数据并保存到 {output_file}")