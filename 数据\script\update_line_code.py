from pymongo import MongoClient

# 连接MongoDB
client = MongoClient('mongodb://localhost:27017/')
db = client['your_database_name']  # 替换为您的数据库名称
collection = db['your_collection_name']  # 替换为您的集合名称

# 更新所有文档，添加lineCode字段
update_result = collection.update_many(
    {},  # 空查询条件表示匹配所有文档
    {
        '$set': {
            'lineCode': 'SMT1-5'
        }
    }
)

print(f"已更新 {update_result.modified_count} 条文档") 