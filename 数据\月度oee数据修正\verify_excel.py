#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Excel文件中的公式和格式
"""

from openpyxl import load_workbook

def verify_excel_file(file_path):
    """验证Excel文件中的公式"""
    print(f"验证文件: {file_path}")
    
    try:
        wb = load_workbook(file_path, data_only=False)  # 不计算公式值，显示公式
        ws = wb.active
        
        print(f"工作表名称: {ws.title}")
        print(f"总行数: {ws.max_row}")
        print(f"总列数: {ws.max_column}")
        
        # 显示表头
        headers = [cell.value for cell in ws[1]]
        print(f"\n表头: {headers}")
        
        # 显示汇总行（最后一行）
        summary_row = ws.max_row
        print(f"\n汇总行 (第{summary_row}行):")
        
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=summary_row, column=col)
            header = headers[col-1] if col-1 < len(headers) else f"列{col}"
            
            if cell.value is not None:
                if isinstance(cell.value, str) and cell.value.startswith('='):
                    print(f"  {header}: {cell.value} (公式)")
                else:
                    print(f"  {header}: {cell.value}")
        
        # 检查百分比格式
        print(f"\n百分比列格式检查:")
        percent_cols = ['实际运转率', '实际有效生产率', '实际良品率', '实际OEE']
        for col_name in percent_cols:
            if col_name in headers:
                col_idx = headers.index(col_name) + 1
                cell = ws.cell(row=summary_row, column=col_idx)
                print(f"  {col_name}: 格式={cell.number_format}, 值={cell.value}")
        
        # 现在加载计算后的值
        wb_calc = load_workbook(file_path, data_only=True)
        ws_calc = wb_calc.active
        
        print(f"\n汇总行计算结果:")
        for col in range(1, ws_calc.max_column + 1):
            cell = ws_calc.cell(row=summary_row, column=col)
            header = headers[col-1] if col-1 < len(headers) else f"列{col}"
            
            if cell.value is not None:
                print(f"  {header}: {cell.value}")
                
    except Exception as e:
        print(f"验证过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_excel_file("1车间5月oee_处理结果.xlsx")
