# 一车间所有线体oee指标汇总

以下为105线指标

![alt text](../assets/images/image.png)

## 前端界面

![alt text](../assets/images/Snipaste_2024-11-28_13-20-05.png)

前端界面类似如上截图，将5条线数据进行汇总。

* 增加车间切换，eg,一车间，二车间。
* 增加开始时间和结束时间查询条件，默认为当日上午8点到次日上午8点。
* 另外每个卡片列表允许点击跳转到对应线体的oee界面。


## 后端接口

### 1、车间切换，获取所有车间数据，查询车间下有哪些线体。

#### 车间数据

* 车间表结构

```txt
{
    id：string //主键ID,
    code: string //车间编码,
    name: string //车间名称
}
```

* 数据

```txt
code,name
SMT1,车间一楼
SMT2,车间二楼
```

oee库中的t_production_line 增加车间编码。用于关联查询对应车间下的所有线体。

### 2、依据第一个接口获取的车间下全部的线体编码获取，依据线体编码查询每条线的oee指标

#### 接口地址

AnalyzeController.java 文件中的getLineOee方法

```java
/**
 * 获取线体OEE等指标
 * @param query 包含线体编码、开始时间和结束时间的查询条件
 * @return OEE等指标结果
 */
@PostMapping("/line-oee")
public OeeResult getLineOee(@RequestBody AnalyzeQuery query) {
	return analyzeService.getLineOee(query);
}
```

## 项目地址

* 后端：ssh://git@192.168.2.201:2222/OEE/OEE-backend.git
* 前端：ssh://git@192.168.2.201:2222/OEE/OEE-frontend.git