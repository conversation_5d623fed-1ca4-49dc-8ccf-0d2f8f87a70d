设备运行状态时间
PowerON: 设备开机总时间（从开机到关机的总时间）。

PowerON=10217.42：设备开机总时间为 10217.42 秒。

Prod: 设备生产总时间（包括实际生产和空闲时间）。

Prod=10217.42：设备生产总时间为 10217.42 秒。

Actual: 设备实际生产时间（真正用于生产的时间）。

Actual=8165.65：设备实际生产时间为 8165.65 秒。

Load: 设备上料时间（加载材料的时间）。

Load=942.35：设备上料时间为 942.35 秒。

BRcg: 设备抛料时间（抛料或重新上料的时间）。

BRcg=151.45：设备抛料时间为 151.45 秒。

Mount: 设备贴装时间（实际贴装元件的时间）。

Mount=7071.84：设备贴装时间为 7071.84 秒。

设备空闲时间
Idle: 设备空闲时间（未进行任何操作的时间）。

Idle=0.00：设备空闲时间为 0 秒。

设备停止时间
TotalStop: 设备总停止时间（所有停止时间的总和）。

TotalStop=2051.77：设备总停止时间为 2051.77 秒。

Fwait: 前道等待时间（等待前道工序完成的时间）。

Fwait=1264.26：前道等待时间为 1264.26 秒。

Rwait: 后道等待时间（等待后道工序完成的时间）。

Rwait=202.79：后道等待时间为 202.79 秒。

Swait: 供料器等待时间（等待供料器准备的时间）。

Swait=0.00：供料器等待时间为 0 秒。

Cwait: 传送带等待时间（等待传送带准备的时间）。

Cwait=0.00：传送带等待时间为 0 秒。

Bwait: 板子等待时间（等待板子到位的时间）。

Bwait=0.00：板子等待时间为 0 秒。

Pwait: 程序等待时间（等待程序准备的时间）。

Pwait=0.00：程序等待时间为 0 秒。

SCStop: 软件控制停止时间（由软件控制的停止时间）。

SCStop=0.00：软件控制停止时间为 0 秒。

SCEStop: 软件控制紧急停止时间（由软件控制的紧急停止时间）。

SCEStop=31.17：软件控制紧急停止时间为 31.17 秒。

OthrStop: 其他停止时间（未分类的停止时间）。

OthrStop=0.00：其他停止时间为 0 秒。

CnvStop: 传送带停止时间（传送带相关的停止时间）。

CnvStop=0.00：传送带停止时间为 0 秒。

BRcgStop: 抛料停止时间（抛料相关的停止时间）。

BRcgStop=0.00：抛料停止时间为 0 秒。

MHRcgStop: 手动抛料停止时间（手动抛料相关的停止时间）。

MHRcgStop=0.00：手动抛料停止时间为 0 秒。

FBStop: 反馈停止时间（反馈相关的停止时间）。

FBStop=0.00：反馈停止时间为 0 秒。

BNDRcgStop: 绑定抛料停止时间（绑定抛料相关的停止时间）。

BNDRcgStop=0.00：绑定抛料停止时间为 0 秒。

PRDStop: 生产停止时间（生产相关的停止时间）。

PRDStop=6331.94：生产停止时间为 6331.94 秒。

JudgeStop: 判定停止时间（判定相关的停止时间）。

JudgeStop=0.00：判定停止时间为 0 秒。

OtherLStop: 其他逻辑停止时间（其他逻辑相关的停止时间）。

OtherLStop=0.00：其他逻辑停止时间为 0 秒。

BNDStop: 绑定停止时间（绑定相关的停止时间）。

BNDStop=0.00：绑定停止时间为 0 秒。

McFwait: 机器前道等待时间（机器前道工序的等待时间）。

McFwait=1264.26：机器前道等待时间为 1264.26 秒。

McRwait: 机器后道等待时间（机器后道工序的等待时间）。

McRwait=202.79：机器后道等待时间为 202.79 秒。

JointPassWait: 联合通过等待时间（联合通过相关的等待时间）。

JointPassWait=0.00：联合通过等待时间为 0 秒。

PPIStop: PPI 停止时间（PPI 相关的停止时间）。

PPIStop=0.00：PPI 停止时间为 0 秒。

Simulation: 模拟时间（模拟运行的时间）。

Simulation=0.00：模拟时间为 0 秒。

错误相关时间
Trbl: 故障时间（设备故障的时间）。

Trbl=0.00：故障时间为 0 秒。

CPErr: 元件贴装错误时间（元件贴装错误的时间）。

CPErr=357.33：元件贴装错误时间为 357.33 秒。

CRErr: 元件识别错误时间（元件识别错误的时间）。

CRErr=196.22：元件识别错误时间为 196.22 秒。

CDErr: 元件数据错误时间（元件数据错误的时间）。

CDErr=0.00：元件数据错误时间为 0 秒。

CMErr: 元件缺失错误时间（元件缺失错误的时间）。

CMErr=0.00：元件缺失错误时间为 0 秒。

CTErr: 元件类型错误时间（元件类型错误的时间）。

CTErr=0.00：元件类型错误时间为 0 秒。

TRSErr: 传送错误时间（传送相关的错误时间）。

TRSErr=0.00：传送错误时间为 0 秒。

总结
设备运行时间：PowerON 和 Prod 是设备的总运行时间。

实际生产时间：Actual 是真正用于生产的时间。

停止时间：TotalStop 是所有停止时间的总和，包括等待时间、故障时间等。

错误时间：CPErr 和 CRErr 是主要的错误时间。
