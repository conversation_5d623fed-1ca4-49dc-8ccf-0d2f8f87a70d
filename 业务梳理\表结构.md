# 表结构

t_workshop 车间表

```txt
{
  "_id": {
    "$oid": "67aabddc5deedda98ca2797e"
  },
  "code": "WH-001",
  "name": "芜湖工厂一车间"
}
```

t_production_line 生产线表

```txt
{
  "_id": {
    "$oid": "67904c782ed1970a608bba7f"
  },
  "code": "SMT1-1",
  "name": "芜湖工厂SMT101",
  "type": "singleTrack",
  "workshop_code": "WH-001"    所属车间编码
}
```

t_device 设备表

```txt
{
  "_id": {
    "$oid": "67bab7ecc37d94df1f6cab01"
  },
  "code": "HJ-PR-2122-1",    //设备编码
  "name": "贴片机1",          //设备名称
  "type": "smt_npm_reporter2",    //设备类型
  "category": "SMT",         //设备分类
  "track": "track1",       //所属轨道， 
  "line_id": "LS1-1",     所属线体编码
  "client_ip": "************",    //设备客户端ip
  "client_port": 8093,    //设备客户端端口
  "sort": 2,        //设备在线体顺序
  "enable": 1,     //设备是否启用   1启用  0禁用
  "group": 1       //
}
```

## 字典

```txt
/**
 * 设备类型
 */
public enum DeviceType {
 print_gkg, // gkg打印机
 print_dek, // dek打印机
 smt_samsung, // 三星贴片机
 smt_npm, // 松下贴片机
 smt_npm_reporter, // 松下贴片机
 smt_npm_reporter2, // 松下贴片机2
 smt_yamaha, // 雅马哈贴片机
 aoi_juzte, // 矩子aoi
 aoi_yamaha, // 雅马哈aoi
 aoi_viscom; // viscomaoi
}

```

```txt
/**
 * 设备分类
 */
public enum DeviceCategory {
 AOI, // AOI设备
 SMT, // 贴片机
 PRINTER, // 印刷机
 SPI; // SPI
}

```
