import csv

input_file = 'd:/Projects/persist/oee/oee-doc/1.csv'
output_file = 'd:/Projects/persist/oee/oee-doc/1_updated.csv'

with open(input_file, 'r', encoding='utf-8') as infile, \
     open(output_file, 'w', encoding='utf-8', newline='') as outfile:
    
    reader = csv.reader(infile)
    writer = csv.writer(outfile)
    
    for row in reader:
        # Keep only first 4 columns
        new_row = row[:4]
        writer.writerow(new_row)

print("Column removed successfully!")