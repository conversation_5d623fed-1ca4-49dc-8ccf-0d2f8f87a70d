[
    // 阶段1：时间范围匹配并过滤空产品型号
    {
      $match: {
        time: {
          $gte: ISODate("2025-04-01T00:00:00Z"),
          $lte: ISODate("2025-05-01T00:00:00Z")
        },
        productmodel: { $ne: "" }
      }
    },
  
    // 阶段2：时间排序
    { $sort: { time: 1 } },
  
    // 阶段3：获取前一个产品型号
    {
      $setWindowFields: {
        partitionBy: null,
        sortBy: { time: 1, totaltime: -1 },
        output: {
          prevProduct: {
            $shift: {
              output: "$productmodel",
              by: -1,
              default: null
            }
          }
        }
      }
    },
  
    // 阶段4：生成变化分组ID
    {
      $setWindowFields: {
        sortBy: { time: 1, totaltime: -1 },
        output: {
          groupId: {
            $sum: {
              $cond: {
                if: {
                  $ne: [
                    "$productmodel",
                    "$prevProduct"
                  ]
                },
                then: 1,
                else: 0
              }
            },
            window: {
              documents: ["unbounded", "current"]
            }
          }
        }
      }
    },
  
    // 阶段5：获取各指标前值
    {
      $setWindowFields: {
        sortBy: { time: 1, totaltime: -1 },
        output: {
          previousActualTime: {
            $shift: {
              output: "$actualtime",
              by: -1
            }
          },
          previousStopTime: {
            $shift: { output: "$stoptime", by: -1 }
          },
          previousTotalTime: {
            $shift: { output: "$totaltime", by: -1 }
          },
          previousBoard: {
            $shift: {
              output: "$raw.count.board",
              by: -1
            }
          },
          previousModule: {
            $shift: {
              output: "$raw.count.module",
              by: -1
            }
          },
          previousScstop: {
            $shift: {
              output: "$raw.time.scstop",
              by: -1
            }
          },
          previousScestop: {
            $shift: {
              output: "$raw.time.scestop",
              by: -1
            }
          }
        }
      }
    },
  
    // 阶段6：计算差值
    {
      $addFields: {
        actualTimeResult: {
          $cond: {
            if: {
              $gte: [
                "$actualtime",
                "$previousActualTime"
              ]
            },
            then: {
              $subtract: [
                "$actualtime",
                "$previousActualTime"
              ]
            },
            else: "$actualtime"
          }
        },
        stopTimeResult: {
          $cond: {
            if: {
              $gte: [
                "$stoptime",
                "$previousStopTime"
              ]
            },
            then: {
              $subtract: [
                "$stoptime",
                "$previousStopTime"
              ]
            },
            else: "$stoptime"
          }
        },
        totalTimeResult: {
          $cond: {
            if: {
              $gte: [
                "$totaltime",
                "$previousTotalTime"
              ]
            },
            then: {
              $subtract: [
                "$totaltime",
                "$previousTotalTime"
              ]
            },
            else: "$totaltime"
          }
        },
        boardResult: {
          $cond: {
            if: {
              $gte: [
                "$raw.count.board",
                "$previousBoard"
              ]
            },
            then: {
              $subtract: [
                "$raw.count.board",
                "$previousBoard"
              ]
            },
            else: "$raw.count.board"
          }
        },
        moduleResult: {
          $cond: {
            if: {
              $gte: [
                "$raw.count.module",
                "$previousModule"
              ]
            },
            then: {
              $subtract: [
                "$raw.count.module",
                "$previousModule"
              ]
            },
            else: "$raw.count.module"
          }
        },
        scstopResult: {
          $cond: {
            if: {
              $gte: [
                "$raw.time.scstop",
                "$previousScstop"
              ]
            },
            then: {
              $subtract: [
                "$raw.time.scstop",
                "$previousScstop"
              ]
            },
            else: "$raw.time.scstop"
          }
        },
        scestopResult: {
          $cond: {
            if: {
              $gte: [
                "$raw.time.scestop",
                "$previousScestop"
              ]
            },
            then: {
              $subtract: [
                "$raw.time.scestop",
                "$previousScestop"
              ]
            },
            else: "$raw.time.scestop"
          }
        }
      }
    },
  
    // 阶段7：过滤无效记录
    { $match: { actualTimeResult: { $ne: null } } },
  
    // 阶段8：按productmodel和groupId分组统计
    {
      $group: {
        _id: {
          productmodel: "$productmodel",
          groupId: "$groupId"
        },
        actualTimeTotal: {
          $sum: "$actualTimeResult"
        },
        stopTimeTotal: { $sum: "$stopTimeResult" },
        totalTimeTotal: {
          $sum: "$totalTimeResult"
        },
        boardTotal: { $sum: "$boardResult" },
        moduleTotal: { $sum: "$moduleResult" },
        scstopTotal: { $sum: "$scstopResult" },
        scestopTotal: { $sum: "$scestopResult" }
      }
    }
  ]