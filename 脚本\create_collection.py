from pymongo import MongoClient

def create_collections():
    # 连接MongoDB
    client = MongoClient('***********************************************/')
    
    # 获取数据库实例 - 请将'your_database'替换为实际的数据库名
    db = client['oee']
    
    # 需要创建的集合列表
    collections_to_create = [
       "HJ-PR-824_PRODUCTION",
"HJ-PR-825_PRODUCTION",
"HJ-PR-826_PRODUCTION",
"HJ-PR-1164_PRODUCTION",
"HJ-PR-1164_PRODUCTION",
"HJ-PR-021_PRODUCTION",
"HJ-PR-1318_PRODUCTION",
"HJ-PR-605_PRODUCTION",
"HJ-PR-605_PRODUCTION",
"HJ-PR-1127_PRODUCTION",
"HJ-PR-1128_PRODUCTION",
"HJ-PR-1129_PRODUCTION",
"HJ-PR-1131_PRODUCTION",
"HJ-PR-1131_PRODUCTION",
"HJ-PR-1305_PRODUCTION",
"HJ-PR-1298_PRODUCTION",
"HJ-PR-1303_PRODUCTION",
"HJ-PR-1313_PRODUCTION",
"HJ-PR-1301_PRODUCTION",
"HJ-PR-1301_PRODUCTION",
"HJ-PR-1530_PRODUCTION",
"HJ-PR-1605_PRODUCTION",
"HJ-PR-1606_PRODUCTION",
"HJ-PR-1607_PRODUCTION",
"HJ-PR-1614_PRODUCTION",
"HJ-PR-1146_PRODUCTION",
"HJ-PR-1147_PRODUCTION",
"HJ-PR-1148_PRODUCTION",
"HJ-PR-1643_PRODUCTION",
"HJ-PR-1332_PRODUCTION",
"HJ-PR-1333_PRODUCTION",
"HJ-PR-1334_PRODUCTION",
"HJ-PR-054_PRODUCTION",
"HJ-PR-054_PRODUCTION",
"HJ-PR-1644_PRODUCTION",
"HJ-PR-1645_PRODUCTION",
"HJ-PR-1646_PRODUCTION",
"HJ-PR-1619_PRODUCTION",
"HJ-PR-1619_PRODUCTION",
"HJ-PR-1814-1_PRODUCTION",
"HJ-PR-1815-1_PRODUCTION",
"HJ-PR-1816-1_PRODUCTION",
"HJ-PR-1817-1_PRODUCTION",
"HJ-PR-2027_PRODUCTION",
"HJ-PR-1814-2_PRODUCTION",
"HJ-PR-1815-2_PRODUCTION",
"HJ-PR-1816-2_PRODUCTION",
"HJ-PR-1817-2_PRODUCTION",
"HJ-PR-2027_PRODUCTION",
"HJ-PR-1317_PRODUCTION",
"HJ-PR-1136_PRODUCTION",
"HJ-PR-022_PRODUCTION",
"HJ-LS-070_PRODUCTION",
"HJ-PR-1938_PRODUCTION",
"HJ-PR-319_PRODUCTION",
"HJ-PR-332_PRODUCTION",
"HJ-PR-320_PRODUCTION",
"HJ-PR-1745_PRODUCTION",
"HJ-PR-1745_PRODUCTION",
"HJ-CDPR-027_PRODUCTION",
"HJ-PR-104_PRODUCTION",
"HJ-PR-1216_PRODUCTION",
"HJ-PR-251_PRODUCTION",
"HJ-PR-251_PRODUCTION",
"HJ-PR-263_PRODUCTION",
"HJ-PR-238_PRODUCTION",
"HJ-PR-264_PRODUCTION",
"HJ-PR-250_PRODUCTION",
"HJ-PR-250_PRODUCTION",
"HJ-PR-317_PRODUCTION",
"HJ-PR-331_PRODUCTION",
"HJ-PR-318_PRODUCTION",
"HJ-PR-049_PRODUCTION",
"HJ-PR-049_PRODUCTION",
"HJ-PR-293_PRODUCTION",
"HJ-PR-110_PRODUCTION",
"HJ-PR-1958_PRODUCTION",
"HJ-PR-1958_PRODUCTION",
"HJ-PR-1833_PRODUCTION",
"HJ-PR-1834_PRODUCTION",
"HJ-PR-1835_PRODUCTION",
"HJ-PR-1840_PRODUCTION",
"HJ-PR-1840_PRODUCTION",
"HJ-PR-1960_PRODUCTION",
"HJ-PR-323_PRODUCTION",
"HJ-PR-031_PRODUCTION",
"HJ-PR-032_PRODUCTION",
"HJ-PR-1667_PRODUCTION",
"HJ-PR-1535_PRODUCTION",
"HJ-PR-1536_PRODUCTION",
"HJ-PR-1537_PRODUCTION",
"HJ-PR-1538_PRODUCTION",
"HJ-PR-1546_PRODUCTION",
"HJ-PR-1961-1_PRODUCTION",
"HJ-PR-1961-2_PRODUCTION",
"HJ-PR-1962-1_PRODUCTION",
"HJ-PR-1962-2_PRODUCTION",
"HJ-PR-1966-1_PRODUCTION",
"HJ-PR-1966-2_PRODUCTION",
"HJ-PR-1967-1_PRODUCTION",
"HJ-PR-1967-2_PRODUCTION",
"HJ-PR-2026_PRODUCTION",
"HJ-PR-2026_PRODUCTION",
"HJ-PR-1963-1_PRODUCTION",
"HJ-PR-1963-2_PRODUCTION",
"HJ-PR-1964-1_PRODUCTION",
"HJ-PR-1964-2_PRODUCTION",
"HJ-PR-1965-1_PRODUCTION",
"HJ-PR-1965-2_PRODUCTION",
"HJ-PR-1954-1_PRODUCTION",
"HJ-PR-1954-2_PRODUCTION",
"HJ-PR-2028_PRODUCTION",
"HJ-PR-2028_PRODUCTION",
"HJ-PR-2122-1_PRODUCTION",
"HJ-PR-2121-1_PRODUCTION",
"HJ-PR-2116-1_PRODUCTION",
"HJ-PR-2144_PRODUCTION",
"HJ-PR-2122-2_PRODUCTION",
"HJ-PR-2121-2_PRODUCTION",
"HJ-PR-2116-2_PRODUCTION",
"HJ-PR-2144_PRODUCTION",
"HJ-PR-1797_PRODUCTION",
"HJ-PR-1806_PRODUCTION",
"HJ-PR-1798_PRODUCTION",
"HJ-PR-1668_PRODUCTION",
"HJ-PR-2120-1_PRODUCTION",
"HJ-PR-2119-1_PRODUCTION",
"HJ-PR-2112-1_PRODUCTION",
"HJ-PR-2113-1_PRODUCTION",
"HJ-PR-2145_PRODUCTION",
"HJ-PR-2120-2_PRODUCTION",
"HJ-PR-2119-2_PRODUCTION",
"HJ-PR-2112-2_PRODUCTION",
"HJ-PR-2113-2_PRODUCTION",
"HJ-PR-2145_PRODUCTION",
"HJ-PR-2056_PRODUCTION",
"HJ-PR-1953_PRODUCTION",
"HJ-PR-1952_PRODUCTION",
"HJ-PR-2057_PRODUCTION",
"HJ-PR-2064_PRODUCTION",
"HJ-PR-2114-1_PRODUCTION",
"HJ-PR-2115-1_PRODUCTION",
"HJ-PR-2118-1_PRODUCTION",
"HJ-PR-2117-1_PRODUCTION",
"HJ-PR-2111-1_PRODUCTION",
"HJ-PR-2146_PRODUCTION",
"HJ-PR-2114-2_PRODUCTION",
"HJ-PR-2115-2_PRODUCTION",
"HJ-PR-2118-2_PRODUCTION",
"HJ-PR-2117-2_PRODUCTION",
"HJ-PR-2111-2_PRODUCTION",
"HJ-PR-2146_PRODUCTION"
    ]
    
    # 获取现有集合列表
    existing_collections = db.list_collection_names()
    
    # 遍历需要创建的集合
    for collection_name in collections_to_create:
        if collection_name not in existing_collections:
            try:
                # 创建集合（通过创建一个空文档来实现）
                db.create_collection(collection_name)
                print(f"成功创建集合: {collection_name}")
            except Exception as e:
                print(f"创建集合 {collection_name} 时出错: {str(e)}")
        else:
            print(f"集合已存在，跳过创建: {collection_name}")
    
    print("集合创建操作完成")
    client.close()

if __name__ == "__main__":
    try:
        create_collections()
    except Exception as e:
        print(f"执行过程中出现错误: {str(e)}")