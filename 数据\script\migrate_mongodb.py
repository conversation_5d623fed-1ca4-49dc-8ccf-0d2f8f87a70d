from pymongo import MongoClient

# 源数据库配置
source_client = MongoClient('***********************************************/')
source_db = source_client['oee']

# 目标数据库配置
target_client = MongoClient('***************************************/')
target_db = target_client['oee']

# 获取源数据库中的所有集合名称
collections = source_db.list_collection_names()

# 遍历每个集合并迁移数据
for collection_name in collections:
    print(f"Migrating collection: {collection_name}")
    
    # 获取源集合
    source_collection = source_db[collection_name]
    
    # 获取目标集合
    target_collection = target_db[collection_name]
    
    # 清空目标集合（可选，根据需要决定是否清空）
    target_collection.delete_many({})
    
    # 从源集合中读取数据并插入到目标集合中
    for document in source_collection.find():
        target_collection.insert_one(document)

print("Migration completed.")