[
  {
    $match: {
      time: {
        $gte: ISODate("2025-04-16T00:00:00Z"),
        $lte: ISODate("2025-04-17T00:00:00Z")
      }
    }
  },
  {
    $sort: {
      time: 1
    }
  },
  {
    $setWindowFields: {
      partitionBy: null,
      sortBy: {
        time: 1
      },
      output: {
        prevProduct: {
          $shift: {
            output: "$productmodel",
            by: -1,
            default: null
          }
        }
      }
    }
  },
  {
    $setWindowFields: {
      partitionBy: null,
      sortBy: {
        time: 1
      },
      output: {
        groupId: {
          $sum: {
            $cond: [
              {
                $ne: [
                  "$productmodel",
                  "$prevProduct"
                ]
              },
              1,
              0
            ]
          },
          window: {
            documents: ["unbounded", "current"]
          }
        }
      }
    }
  },
  {
    $setWindowFields: {
      sortBy: {
        time: 1
      },
      output: {
        previousTime: {
          $shift: {
            output: "$time",
            by: -1
          }
        }
      }
    }
  },
  {
    $addFields: {
      actualTimeResult: {
        $cond: {
          if: {
            $gte: [
              {
                $toLong: "$time"
              },
              {
                $toLong: "$previousTime"
              }
            ]
          },
          then: {
            $divide: [
              {
                $subtract: [
                  {
                    $toLong: "$time"
                  },
                  {
                    $toLong: "$previousTime"
                  }
                ]
              },
              1000
            ]
          },
          else: 0
        }
      }
    }
  },
  {
    $match: {
      actualTimeResult: {
        $ne: null
      }
    }
  },
  {
    $group: {
      _id: {
        productmodel: "$productmodel",
        groupId: "$groupId"
      },
      actualTimeTotal: {
        $sum: "$actualTimeResult"
      },
      boardTotal: {
        $sum: 1
      }
    }
  }
]