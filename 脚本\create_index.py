from pymongo import MongoClient, ASCENDING

def create_time_indexes():
    # 连接MongoDB
    client = MongoClient('***********************************************/')
    
    # 获取数据库实例
    db = client['oee']
    
    # 获取所有集合名称
    collections = db.list_collection_names()
    
    # 筛选出以'HJ'开头的集合
    hj_collections = [coll for coll in collections if coll.startswith('HJ')]
    
    # 遍历每个集合并创建time索引
    for collection_name in hj_collections:
        collection = db[collection_name]
        
        try:
            # 创建time字段的索引
            index_name = collection.create_index([("productmodel", ASCENDING)], background=True)
            print(f"成功为集合 {collection_name} 创建time索引: {index_name}")
        except Exception as e:
            print(f"为集合 {collection_name} 创建time索引时出错: {str(e)}")
    
    print("索引创建操作完成")
    client.close()

if __name__ == "__main__":
    try:
        create_time_indexes()
    except Exception as e:
        print(f"执行过程中出现错误: {str(e)}")