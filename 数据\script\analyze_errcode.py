import pandas as pd

# 读取CSV文件
df = pd.read_csv('oee.HJ-PR-1301_PRODUCTION.csv')

# 将errcode按_分割，取第二部分
df['error_type'] = df['_id.errcode'].str.split('_').str[1]

# 统计每种error_type的出现次数和总count
error_stats = df.groupby('error_type')['count'].agg(['count', 'sum']).reset_index()
error_stats.columns = ['error_type', 'occurrences', 'total_count']

# 按total_count降序排序
error_stats = error_stats.sort_values('total_count', ascending=False)

# 将结果写入文件
with open('error_statistics.txt', 'w', encoding='utf-8') as f:
    f.write("Error Type Statistics:\n")
    f.write("====================\n")
    for _, row in error_stats.iterrows():
        f.write(f"Error Type: {row['error_type']}\n")
        f.write(f"Number of Occurrences: {row['occurrences']}\n")
        f.write(f"Total Count: {row['total_count']}\n")
        f.write("--------------------\n")

print("Statistics have been written to error_statistics.txt") 