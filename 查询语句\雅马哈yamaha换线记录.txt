[
  {
    $match: {
      time: {
        $gte: ISODate("2025-04-01T00:00:00Z"),
        $lte: ISODate("2025-05-01T23:59:59Z")
      }
    }
  },
  {
    $sort: {
      time: 1
    }
  },
  {
    $setWindowFields: {
      sortBy: {
        time: 1
      },
      output: {
        prevModel: {
          $shift: {
            output: "$productmodel",
            by: -1,
            default: null
          }
        },
        prevTime: {
          $shift: {
            output: "$time",
            by: -1,
            default: null
          }
        }
      }
    }
  },
  {
    $addFields: {
      isChangeover: {
        $cond: {
          if: {
            $and: [
              {
                $ne: [
                  "$productmodel",
                  "$prevModel"
                ]
              },
              { $ne: ["$prevModel", null] }
            ]
          },
          then: 1,
          else: 0
        }
      },
      // 计算换线开始时间、结束时间和间隔
      changeoverInfo: {
        $cond: {
          if: {
            $and: [
              {
                $ne: [
                  "$productmodel",
                  "$prevModel"
                ]
              },
              { $ne: ["$prevModel", null] }
            ]
          },
          then: {
            startTime: "$prevTime", // 换线开始时间 = 前一条记录的结束时间
            endTime: "$productionstarttime", // 换线结束时间 = 当前记录的生产开始时间
            interval: {
              $divide: [
                {
                  $subtract: [
                    "$productionstarttime",
                    "$prevTime"
                  ]
                }, // 间隔（毫秒）
                1000 // 转换为秒
              ]
            },
            fromModel: "$prevModel", // 换线前型号
            toModel: "$productmodel" // 换线后型号
          },
          else: null
        }
      }
    }
  },
  {
    $match: {
      isChangeover: 1 // 仅保留换线记录
    }
  },
  {
    $project: {
      _id: 0,
      "changeoverInfo.startTime": 1,
      "changeoverInfo.endTime": 1,
      "changeoverInfo.interval": 1,
      "changeoverInfo.fromModel": 1,
      "changeoverInfo.toModel": 1
    }
  }
]