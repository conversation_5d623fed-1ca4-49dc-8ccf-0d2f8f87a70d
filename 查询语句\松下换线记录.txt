[
  // 1. 匹配时间范围和产品型号
  {
    $match: {
      time: {
        $gte: ISODate("2025-05-07T00:00:00Z"),
        $lte: ISODate("2025-05-08T00:00:00Z")
      },
      productmodel: { $ne: "" }
    }
  },

  // 2. 按时间排序
  { $sort: { time: 1 } },

  // 3. 窗口操作获取前一个产品型号和计数
  {
    $setWindowFields: {
      partitionBy: null,
      sortBy: { time: 1, totaltime: -1 },
      output: {
        prevproductmodel: {
          $shift: {
            output: "$productmodel",
            by: -1
          }
        },
        prevcount: {
          $shift: {
            output: "$raw.count.board",
            by: -1
          }
        }
      }
    }
  },

  // 4. 过滤无效记录
  { $match: { prevcount: { $ne: null } } },

  // 5. 添加计算字段
  {
    $addFields: {
      C: {
        $cond: {
          if: {
            $gt: [
              "$raw.count.board",
              "$prevcount"
            ]
          },
          then: {
            $subtract: [
              "$raw.count.board",
              "$prevcount"
            ]
          },
          else: 0
        }
      },
      isDifferentProduct: {
        $ne: [
          "$productmodel",
          "$prevproductmodel"
        ]
      }
    }
  },

  // 6. 生成分组标识符
  {
    $setWindowFields: {
      partitionBy: null,
      sortBy: { time: 1, totaltime: -1 },
      output: {
        groupId: {
          $sum: {
            $cond: [{ $ne: ["$C", 0] }, 1, 0]
          },
          window: {
            documents: ["unbounded", "current"]
          }
        }
      }
    }
  },

  // 7. 筛选C为0的记录
  { $match: { C: 0 } },

  // 8. 按分组标识符分组
  {
    $group: {
      _id: "$groupId",
      firstDoc: { $first: "$$ROOT" },
      lastDoc: { $last: "$$ROOT" },
      allC: { $push: "$C" },
      hasLineChange: {
        $push: "$isDifferentProduct"
      },
      count: { $sum: 1 }
    }
  },

  // 9. 筛选有效换线期间
  {
    $match: {
      $expr: {
        $and: [
          { $eq: [{ $max: "$allC" }, 0] },
          { $anyElementTrue: "$hasLineChange" }
        ]
      }
    }
  },

  // 10. 计算差值和时间差
  {
    $addFields: {
      difference: {
        $subtract: [
          "$lastDoc.raw.count.board",
          "$firstDoc.raw.count.board"
        ]
      },
      timeDifference: {
        $subtract: [
          "$lastDoc.time",
          "$firstDoc.time"
        ]
      },
      startTime: "$firstDoc.time",
      endTime: "$lastDoc.time"
    }
  },

  {
    $project: {
       _id: null,
      startTime: 1,
      endTime: 1,
      timeDifference: 1
    }
  }

  // // 11. 最终汇总
  // {
  //   $group: {
  //     _id: null,
  //     totalDifference: { $sum: "$difference" },
  //     totalTimeDifference: {
  //       $sum: "$timeDifference"
  //     },
  //     blockCount: { $sum: 1 },
  //     blocks: {
  //       $push: {
  //         groupId: "$_id",
  //         startTime: "$startTime",
  //         endTime: "$endTime",
  //         timeDifference: "$timeDifference",
  //         difference: "$difference"
  //       }
  //     }
  //   }
  // },

  // // 12. 格式化输出
  // {
  //   $project: {
  //     _id: 0,
  //     totalDifference: 1,
  //     totalTimeDifference: {
  //       $divide: ["$totalTimeDifference", 1000]
  //     },
  //     blockCount: 1,
  //     blocks: 1
  //   }
  // }
]