### 线体列表集合接口

```struct
Line: {
    name: string
    code: string
}
```

```interface
@GetMapping("/line/list")
List<Line> getList();
```

### 线体列表设备接口

```struct
DeviceInfo: {
    name: string   //设备名称
    code: string   //设备编码
    type: enum     //设备类型
    ct: number   //ct时间
    lineId: string //线体编码
    sort: number //排序
}
```

```enum
DeviceType: {
    print_gkg   // gkg打印机
	smt_samsung // 三星贴片机
	smt_npm // 松下贴片机
	smt_yamaha // 雅马哈贴片机
	aoi_yamaha // 雅马哈aoi
}
```

```interface
@GetMapping("/device/list/")
List<DeviceInfo> getList(queryParam param);  
//依据线体编码、开始时间、结束时间获取设备信息
```

```
queryParam: {
    stratTime: date //开始时间
    endTime: date //结束时间
    lineCoe: sting //线体编码
}
startTime、entTime 可为空，为空时，endTime取当前时间，startTime取endTime减去12小时
```
### 单个设备的日志

前端进入不同设备的页面，就调用对应设备的路径接口，比如松下则路径以smt_npm/
三星则以smt_samsung/
路径对应设备类型

#### 松下生产日志列表

```logInfo
logid: 日志id
logName: 日志名称    //对应collection中的fileName
logTime: 日志时间    //对应collection中的logTime
```

```interface
// 获取时间段内的日志信息
@PostMapping("/smt_npm/production/log/page")
List<logInfo> pageProductionLog(@RequestBody PageableParam<queryParam> param);
```

```
queryParam: {
    deviceCode: string //设备编码,用于确定是那个collection
    startTime: date //开始时间
    endTime: date // 结束时间
}
```
#### 单个生产日志

```struct
// 松下日志对象
npmLog: {

}
```
```interface
// 获取时间段内的日志信息
@GetMapping("/smt_npm/production/log/{id}/")
List<npmLog> getProductionLog(String deviceCode);
```
### 获取指定时间的贴片程式

@GetMapping("/product/list")
List<Product> getProductList(QueryParam param);

```struct
Product: {
    name: string // 产品名称
    date: date   // 产品日志的起始时间
}
```

```struct
QueryParam: {
    startTime: date // 开始时间
    endTime: date // 结束时间
    lineCode: string // 线体编码
}
```

#### 接口逻辑

1、根据线体编码从t_device中获取该线体下全部设备信息。
2、获取类型以smt开头的设备，按照sort排序选择第一个，获取设备编码和设备类型。
3、按照设备类型进行不同查询


### 获取标准产能

