[
  {
    "$match": {
      "time": {
        "$gte": ISODate("2023-01-01T00:00:00Z"),
        "$lte": ISODate("2023-01-31T23:59:59Z")
      },
      "normalflag": true
    }
  },
  {
    "$sort": { "time": 1 }
  },
  {
    "$group": {
      "_id": null,
      "docs": { "$push": "$$ROOT" }
    }
  },
  {
    "$project": {
      "result": {
        "$reduce": {
          "input": "$docs",
          "initialValue": {
            "previousProductModel": null,
            "previousEndtime": null,
            "changeoverNum": 0,
            "changeoverTime": 0
          },
          "in": {
            "previousProductModel": "$$this.productmodel",
            "previousEndtime": "$$this.endtime",
            "changeoverNum": {
              "$cond": {
                "if": {
                  "$and": [
                    { "$ne": ["$$this.productmodel", "$$value.previousProductModel"] },
                    { "$ne": ["$$value.previousProductModel", null] }
                  ]
                },
                "then": { "$add": ["$$value.changeoverNum", 1] },
                "else": "$$value.changeoverNum"
              }
            },
            "changeoverTime": {
              "$cond": {
                "if": {
                  "$and": [
                    { "$ne": ["$$this.productmodel", "$$value.previousProductModel"] },
                    { "$ne": ["$$value.previousEndtime", null] }
                  ]
                },
                "then": {
                  "$add": [
                    "$$value.changeoverTime",
                    { "$divide": [
                        { "$subtract": ["$$this.starttime", "$$value.previousEndtime"] },
                        1000
                      ]
                    }
                  ]
                },
                "else": "$$value.changeoverTime"
              }
            }
          }
        }
      }
    }
  },
  {
    "$project": {
      "_id": 0,
      "changeoverNum": "$result.changeoverNum",
      "changeoverTime": "$result.changeoverTime"
    }
  }
]