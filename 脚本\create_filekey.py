from pymongo import MongoClient, ASCENDING
from pymongo.errors import Duplicate<PERSON>eyError

def handle_duplicates(collection):
    # 查找所有重复的filekey
    pipeline = [
        {"$group": {
            "_id": "$filekey",
            "count": {"$sum": 1},
            "docs": {"$push": "$_id"}
        }},
        {"$match": {
            "count": {"$gt": 1}
        }}
    ]
    
    duplicates = list(collection.aggregate(pipeline))
    
    # 处理重复文档
    for dup in duplicates:
        # 保留第一个文档，删除其他重复文档
        docs_to_remove = dup['docs'][1:]  # 跳过第一个文档
        collection.delete_many({"_id": {"$in": docs_to_remove}})
        print(f"已删除 {len(docs_to_remove)} 个重复的filekey={dup['_id']}的文档")

def create_filekey_indexes():
    # 连接MongoDB
    client = MongoClient('************************************************')
    
    # 获取数据库实例
    db = client['oee']
    
    # 获取所有集合名称
    collections = db.list_collection_names()
    
    # 筛选出以'HJ'开头的集合
    hj_collections = [coll for coll in collections if coll.startswith('HJ')]
    
    # 遍历每个集合并创建filekey唯一索引
    for collection_name in hj_collections:
        collection = db[collection_name]
        
        try:
            # 尝试创建filekey字段的唯一索引
            index_name = collection.create_index(
                [("filekey", ASCENDING)], 
                unique=True,
                background=True
            )
            print(f"成功为集合 {collection_name} 创建filekey唯一索引: {index_name}")
        except DuplicateKeyError:
            print(f"集合 {collection_name} 存在重复的filekey，正在处理...")
            
            # 检查索引是否存在
            existing_indexes = collection.list_indexes()
            for index in existing_indexes:
                if 'filekey' in index['key']:
                    collection.drop_index(index['name'])
                    print(f"已删除现有的filekey索引")
                    break
            
            # 处理重复数据
            handle_duplicates(collection)
            
            # 重新创建唯一索引
            try:
                index_name = collection.create_index(
                    [("filekey", ASCENDING)], 
                    unique=True,
                    background=True
                )
                print(f"成功为集合 {collection_name} 重新创建filekey唯一索引: {index_name}")
            except Exception as e:
                print(f"为集合 {collection_name} 重新创建索引时出错: {str(e)}")
        except Exception as e:
            print(f"为集合 {collection_name} 创建索引时出错: {str(e)}")
    
    print("索引创建操作完成")
    client.close()

if __name__ == "__main__":
    try:
        create_filekey_indexes()
    except Exception as e:
        print(f"执行过程中出现错误: {str(e)}")