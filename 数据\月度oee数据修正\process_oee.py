import pandas as pd
from openpyxl import load_workbook
import os

def process_oee_file(input_file, output_file):
    """
    处理OEE Excel文件：
    1. 在指定列添加百分号格式
    2. 添加汇总行并插入计算公式

    参数:
        input_file: 输入Excel文件路径
        output_file: 输出Excel文件路径
    """
    print(f"正在处理文件: {input_file}")

    # 读取Excel文件
    df = pd.read_excel(input_file)
    print(f"原始数据行数: {len(df)}")
    print(f"列名: {df.columns.tolist()}")

    # 保存原始数值数据用于计算
    original_df = df.copy()

    # 需要格式化为百分数的列
    percent_columns = ['实际有效生产率', '实际运转率', '实际良品率', '实际OEE']

    # 检查数据类型并转换百分数格式
    for col in percent_columns:
        if col in df.columns:
            # 检查数据是否已经是百分数格式（大于1的值认为是百分数）
            if df[col].max() > 1:
                # 数据是百分数形式（如58.86），需要除以100
                df[col] = df[col] / 100
            # 格式化为百分数字符串
            df[col] = df[col].apply(lambda x: f"{x:.2%}" if pd.notna(x) else "")
            print(f"已格式化列: {col}")

    # 创建汇总行
    summary_row = {}

    # 需要求和的列
    sum_columns = ['开班时间(h)', '运机时间(h)', '停机时间(h)', '换线次数', '换线时间(h)',
                   '标准生产数(件)', '实际生产数(件)', '标准产品点数', '实际产品点数', '不良品数']

    # 计算各列总和
    for col in sum_columns:
        if col in original_df.columns:
            summary_row[col] = original_df[col].sum()
            print(f"{col} 总和: {summary_row[col]}")

    # 计算汇总的关键指标（使用原始数值）
    try:
        # 汇总实际运转率 = 运机时间总和 / (运机时间总和 + 停机时间总和)
        total_runtime = summary_row.get('运机时间(h)', 0)
        total_downtime = summary_row.get('停机时间(h)', 0)
        if total_runtime + total_downtime > 0:
            runtime_rate = total_runtime / (total_runtime + total_downtime)
            summary_row['实际运转率'] = f"{runtime_rate:.2%}"
            print(f"汇总实际运转率: {runtime_rate:.2%}")

        # 汇总实际有效生产率 = 实际生产数总和 / 标准生产数总和
        total_actual_output = summary_row.get('实际生产数(件)', 0)
        total_standard_output = summary_row.get('标准生产数(件)', 0)
        if total_standard_output > 0:
            productivity = total_actual_output / total_standard_output
            summary_row['实际有效生产率'] = f"{productivity:.2%}"
            print(f"汇总实际有效生产率: {productivity:.2%}")

        # 汇总实际良品率 = (实际生产数总和 - 不良品数总和) / 实际生产数总和
        if '不良品数' in original_df.columns and total_actual_output > 0:
            total_defective = summary_row.get('不良品数', 0)
            yield_rate = (total_actual_output - total_defective) / total_actual_output
            summary_row['实际良品率'] = f"{yield_rate:.2%}"
            print(f"汇总实际良品率: {yield_rate:.2%}")

            # 汇总OEE = 实际运转率 × 实际有效生产率 × 实际良品率
            if 'runtime_rate' in locals() and 'productivity' in locals():
                oee = runtime_rate * productivity * yield_rate
                summary_row['实际OEE'] = f"{oee:.2%}"
                print(f"汇总OEE: {oee:.2%}")

    except Exception as e:
        print(f"计算汇总指标时出错: {e}")

    # 添加汇总行标识
    if '线体名称' in df.columns:
        summary_row['线体名称'] = '汇总'

    # 将汇总行添加到DataFrame
    summary_df = pd.DataFrame([summary_row])
    df_with_summary = pd.concat([df, summary_df], ignore_index=True)

    # 保存到Excel文件
    df_with_summary.to_excel(output_file, index=False)
    print(f"文件已保存到: {output_file}")

    # 使用openpyxl添加公式到汇总行
    add_formulas_to_summary(output_file, len(df) + 2)  # +2 因为有表头和从1开始计数

    return df_with_summary

def add_formulas_to_summary(file_path, summary_row_num):
    """
    在汇总行添加Excel公式

    参数:
        file_path: Excel文件路径
        summary_row_num: 汇总行的行号
    """
    try:
        wb = load_workbook(file_path)
        ws = wb.active

        # 获取列索引映射
        headers = [cell.value for cell in ws[1]]
        col_map = {header: idx + 1 for idx, header in enumerate(headers) if header}

        print(f"在第{summary_row_num}行添加公式...")

        # 添加公式到汇总行
        data_end_row = summary_row_num - 1  # 数据的最后一行

        # 获取列字母的辅助函数
        def get_column_letter(col_num):
            """将列号转换为Excel列字母"""
            if col_num <= 26:
                return chr(64 + col_num)
            else:
                # 处理超过26列的情况 (AA, AB, etc.)
                first = chr(64 + ((col_num - 1) // 26))
                second = chr(64 + ((col_num - 1) % 26) + 1)
                return first + second

        # 实际运转率公式: =SUM(运机时间列)/(SUM(运机时间列)+SUM(停机时间列))
        if '运机时间(h)' in col_map and '停机时间(h)' in col_map and '实际运转率' in col_map:
            runtime_col = get_column_letter(col_map['运机时间(h)'])
            downtime_col = get_column_letter(col_map['停机时间(h)'])
            rate_col = get_column_letter(col_map['实际运转率'])
            formula = f"=SUM({runtime_col}2:{runtime_col}{data_end_row})/(SUM({runtime_col}2:{runtime_col}{data_end_row})+SUM({downtime_col}2:{downtime_col}{data_end_row}))"
            ws[f"{rate_col}{summary_row_num}"] = formula
            print(f"实际运转率公式: {formula}")

        # 实际有效生产率公式: =SUM(实际生产数)/SUM(标准生产数)
        if '实际生产数(件)' in col_map and '标准生产数(件)' in col_map and '实际有效生产率' in col_map:
            actual_col = get_column_letter(col_map['实际生产数(件)'])
            standard_col = get_column_letter(col_map['标准生产数(件)'])
            prod_col = get_column_letter(col_map['实际有效生产率'])
            formula = f"=SUM({actual_col}2:{actual_col}{data_end_row})/SUM({standard_col}2:{standard_col}{data_end_row})"
            ws[f"{prod_col}{summary_row_num}"] = formula
            print(f"实际有效生产率公式: {formula}")

        # 实际良品率公式: =(SUM(实际生产数)-SUM(不良品数))/SUM(实际生产数)
        if '实际生产数(件)' in col_map and '不良品数' in col_map and '实际良品率' in col_map:
            actual_col = get_column_letter(col_map['实际生产数(件)'])
            defect_col = get_column_letter(col_map['不良品数'])
            yield_col = get_column_letter(col_map['实际良品率'])
            formula = f"=(SUM({actual_col}2:{actual_col}{data_end_row})-SUM({defect_col}2:{defect_col}{data_end_row}))/SUM({actual_col}2:{actual_col}{data_end_row})"
            ws[f"{yield_col}{summary_row_num}"] = formula
            print(f"实际良品率公式: {formula}")

        # OEE公式: =实际运转率*实际有效生产率*实际良品率
        if '实际运转率' in col_map and '实际有效生产率' in col_map and '实际良品率' in col_map and '实际OEE' in col_map:
            rate_col = get_column_letter(col_map['实际运转率'])
            prod_col = get_column_letter(col_map['实际有效生产率'])
            yield_col = get_column_letter(col_map['实际良品率'])
            oee_col = get_column_letter(col_map['实际OEE'])
            formula = f"={rate_col}{summary_row_num}*{prod_col}{summary_row_num}*{yield_col}{summary_row_num}"
            ws[f"{oee_col}{summary_row_num}"] = formula
            print(f"OEE公式: {formula}")

        # 设置百分数格式
        percent_columns = ['实际有效生产率', '实际运转率', '实际良品率', '实际OEE']
        for col_name in percent_columns:
            if col_name in col_map:
                col_letter = chr(64 + col_map[col_name])
                for row in range(2, summary_row_num + 1):
                    cell = ws[f"{col_letter}{row}"]
                    cell.number_format = '0.00%'

        wb.save(file_path)
        print("公式添加完成！")

    except Exception as e:
        print(f"添加公式时出错: {e}")

def main():
    """
    主函数：处理1车间5月OEE数据
    """
    input_file = "1车间5月oee.xlsx"
    output_file = "1车间5月oee_处理结果.xlsx"

    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 - {input_file}")
        return

    try:
        result_df = process_oee_file(input_file, output_file)
        print("\n处理完成！")
        print(f"输出文件: {output_file}")
        print(f"处理后数据行数: {len(result_df)}")

        # 显示汇总行数据
        print("\n汇总行数据:")
        print(result_df.iloc[-1])

    except Exception as e:
        print(f"处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    
