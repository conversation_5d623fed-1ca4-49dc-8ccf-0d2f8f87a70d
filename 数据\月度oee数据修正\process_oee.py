import pandas as pd
import numpy as np

def process_oee_file(input_file, output_file):
    """
    处理OEE Excel文件：
    1. 在指定列添加百分号
    2. 添加汇总行计算关键指标
    
    参数:
        input_file: 输入Excel文件路径
        output_file: 输出Excel文件路径
    """
    # 读取Excel文件
    df = pd.read_excel(input_file)
    
    # 添加百分号的列
    percent_columns = ['实际有效生产率', '实际运转率', '实际良品率', '实际OEE']
    
    # 格式化百分数列（添加%符号）
    for col in percent_columns:
        if col in df.columns:
            # 确保是数值类型
            if df[col].dtype in [np.float64, np.int64]:
                df[col] = df[col].apply(lambda x: f"{x:.2%}")
            else:
                print(f"警告: 列 '{col}' 不是数值类型，跳过格式化")
    
    # 添加汇总行
    summary_row = {}
    
    # 计算各列总和
    sum_columns = ['运机时间(h)', '停机时间(h)', '换线次数', '换线时间(h)', 
                   '标准生产数(件)', '实际生产数(件)', '标准产品点数', '实际产品点数']
    
    for col in sum_columns:
        if col in df.columns:
            summary_row[col] = df[col].sum()
    
    # 计算汇总指标
    try:
        # 汇总实际运转率 = 运机时间总和 / (运机时间总和 + 停机时间总和)
        total_runtime = summary_row.get('运机时间(h)', 0)
        total_downtime = summary_row.get('停机时间(h)', 0)
        if total_runtime + total_downtime > 0:
            summary_row['实际运转率'] = f"{total_runtime / (total_runtime + total_downtime):.2%}"
        
        # 汇总实际有效生产率 = 实际生产数总和 / 标准生产数总和
        total_actual_output = summary_row.get('实际生产数(件)', 0)
        total_standard_output = summary_row.get('标准生产数(件)', 0)
        if total_standard_output > 0:
            summary_row['实际有效生产率'] = f"{total_actual_output / total_standard_output:.2%}"
        
        # 汇总实际良品率 = (实际生产数总和 - 不良品数总和) / 实际生产数总和
        # 假设有'不良品数'列
        if '不良品数' in df.columns:
            total_defective = df['不良品数'].sum()
            if total_actual_output > 0:
                summary_row['实际良品率'] = f"{(total_actual_output - total_defective) / total_actual_output:.2%}"
        
        # 汇总OEE = 实际运转率 × 实际有效生产率 × 实际良品率
        if '实际运转率' in summary_row and '实际有效生产率' in summary_row and '实际良品率' in summary_row:
            runtime_rate = float(summary_row['实际运转率'].strip('%'))/100
            productivity = float(summary_row['实际有效生产率'].strip('%'))/100
            yield_rate = float(summary_row['实际良品率'].strip('%'))/100
            summary_row['实际OEE'] = f"{runtime_rate * productivity * yield_rate:.2%}"
    
    except KeyError as e:
        print(f"计算错误: 缺少必要列 {e}")
    
    # 添加汇总行
    df_summary = pd.DataFrame([summary_row])
    df = pd.concat([df, df_summary], ignore_index=True)
    
